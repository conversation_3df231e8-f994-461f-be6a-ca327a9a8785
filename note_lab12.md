# Lab12 - LTI系统响应仿真笔记

## 12.2 实验预习要求

### (1) 复习LTI系统的性质

**线性时不变(LTI)系统的基本性质：**

#### 1. 线性性质
- **叠加性(可加性)**：如果输入x₁(t)产生输出y₁(t)，输入x₂(t)产生输出y₂(t)，则输入x₁(t)+x₂(t)产生输出y₁(t)+y₂(t)
- **齐次性(比例性)**：如果输入x(t)产生输出y(t)，则输入ax(t)产生输出ay(t)，其中a为常数
- **数学表达**：T[ax₁(t) + bx₂(t)] = aT[x₁(t)] + bT[x₂(t)] = ay₁(t) + by₂(t)

#### 2. 时不变性质
- **定义**：系统的特性不随时间变化
- **数学表达**：如果输入x(t)产生输出y(t)，则输入x(t-t₀)产生输出y(t-t₀)
- **物理意义**：系统参数不随时间变化，今天和明天对同样输入的响应相同

#### 3. LTI系统的重要特性
- **因果性**：输出只依赖于当前和过去的输入，不依赖于未来的输入
- **稳定性**：有界输入产生有界输出(BIBO稳定)
- **记忆性**：系统输出依赖于输入的历史值

#### 4. LTI系统的分析方法
- **时域分析**：冲激响应h(t)，卷积积分
- **频域分析**：传递函数H(s)，拉普拉斯变换
- **状态空间分析**：状态方程和输出方程

### (2) 复习求解简单的微分方程

**常系数线性微分方程的求解方法：**

#### 1. 一阶常系数线性微分方程
**标准形式**：y'(t) + ay(t) = f(t)

**求解步骤**：
1. **齐次解**：y'(t) + ay(t) = 0 → yₕ(t) = Ce^(-at)
2. **特解**：根据f(t)的形式假设特解形式
3. **通解**：y(t) = yₕ(t) + yₚ(t)
4. **利用初始条件**：确定常数C

#### 2. 二阶常系数线性微分方程
**标准形式**：y''(t) + by'(t) + cy(t) = f(t)

**求解步骤**：
1. **特征方程**：r² + br + c = 0
2. **特征根分类**：
   - **两个不同实根**：r₁ ≠ r₂ → yₕ(t) = C₁e^(r₁t) + C₂e^(r₂t)
   - **重根**：r₁ = r₂ = r → yₕ(t) = (C₁ + C₂t)e^(rt)
   - **共轭复根**：r = α ± jβ → y₅(t) = e^(αt)[C₁cos(βt) + C₂sin(βt)]
3. **特解求法**：
   - **常数**：f(t) = A → yₚ = K
   - **指数函数**：f(t) = Ae^(at) → yₚ = Ke^(at)
   - **正弦函数**：f(t) = Asin(ωt) → yₚ = Ksin(ωt) + Lcos(ωt)

#### 3. 本实验的微分方程分析
**方程**：y''(t) + 2y'(t) + y(t) = f'(t) + 2f(t)

**特征方程**：r² + 2r + 1 = 0 → (r + 1)² = 0

**特征根**：r₁ = r₂ = -1 (重根)

**齐次解**：yₕ(t) = (C₁ + C₂t)e^(-t)

**输入信号**：f(t) = e^(-2t)u(t)
- f'(t) = -2e^(-2t)u(t)
- 右端项：f'(t) + 2f(t) = -2e^(-2t) + 2e^(-2t) = 0 (t > 0)

**结论**：当t > 0时，方程变为齐次方程，在零初始条件下解为零。

## 实验目标
使用MATLAB的lsim()函数对连续LTI系统进行数值仿真，分析系统对指数衰减输入的响应特性。

## 实验要求
- ✅ 使用 `lsim()` 函数进行系统响应波形和激励信号波形仿真
- ✅ 使用 `tf()` 函数根据系统微分方程系数生成传递函数
- ✅ 设置步长为 0.1
- ✅ 添加图例 legend

## 系统描述

### 微分方程
```
y''(t) + 2y'(t) + y(t) = f'(t) + 2f(t)
```

### 输入信号
```
f(t) = e^(-2t)u(t)
```
其中 u(t) 是单位阶跃函数。

### 系统函数
```
H(s) = (s + 2) / (s² + 2s + 1)
```

## 理论分析

### 1. 系统特征方程
对应的齐次方程：`y''(t) + 2y'(t) + y(t) = 0`

特征方程：`s² + 2s + 1 = 0`

特征根：`s₁ = s₂ = -1` (重根)

齐次解：`yₕ(t) = (C₁ + C₂t)e^(-t)`

### 2. 输入信号分析
- `f(t) = e^(-2t)u(t)`
- `f'(t) = -2e^(-2t)u(t)` (对于 t > 0)
- 右端项：`f'(t) + 2f(t) = -2e^(-2t) + 2e^(-2t) = 0` (对于 t > 0)

**重要发现**：当 t > 0 时，右端项为 0，系统实际上是齐次的！

### 3. MATLAB仿真方法
使用 MATLAB 的现代系统仿真方法：
- **tf()函数**：根据微分方程系数创建传递函数对象
- **lsim()函数**：线性系统仿真，直接计算系统响应
- **步长设置**：dt = 0.1，时间向量 t = 0:0.1:5
- **图例显示**：使用 DisplayName 和 legend('show')

## 核心代码结构

### 主程序 (`lab12_LTI_system.m`)
1. **参数设置**：时间向量（步长0.1）、传递函数系数
2. **传递函数创建**：使用 `tf(a, b)` 创建系统对象
3. **输入信号生成**：f(t) = e^(-2t)u(t)
4. **系统仿真**：使用 `lsim(sys, f_input, t)` 计算响应
5. **可视化**：绘制激励信号波形和系统响应波形（带图例）
6. **数据输出**：显示关键时刻的数值

### MATLAB关键函数使用
```matlab
% 传递函数创建
a = [1, 2];           % 分子系数 [s, 1] → s + 2
b = [1, 2, 1];        % 分母系数 [s², s, 1] → s² + 2s + 1
sys = tf(a, b);       % 创建传递函数对象

% 线性系统仿真
y_response = lsim(sys, f_input, t);

% 图例设置
plot(t, y, 'DisplayName', '标签名');
legend('show', 'Location', 'northeast');
```

## 预期结果

### 理论分析修正
重新分析传递函数 H(s) = (s + 2)/(s² + 2s + 1)：

1. **分母因式分解**：s² + 2s + 1 = (s + 1)²
2. **传递函数**：H(s) = (s + 2)/(s + 1)²
3. **对于输入** f(t) = e^(-2t)u(t)，其拉普拉斯变换为 F(s) = 1/(s + 2)
4. **输出的拉普拉斯变换**：Y(s) = H(s)F(s) = (s + 2)/[(s + 1)²(s + 2)] = 1/(s + 1)²
5. **时域响应**：y(t) = te^(-t)u(t)

### 数值验证
通过 MATLAB 的 lsim() 函数验证理论分析的正确性。

## 关键学习点

1. **传递函数方法**：tf() 函数创建系统模型
2. **线性系统仿真**：lsim() 函数的使用
3. **LTI系统分析**：传递函数、极点、零点的概念
4. **MATLAB绘图**：图例设置、多子图、DisplayName属性
5. **步长控制**：时间向量的精确设置


## 文件说明

- `lab12_LTI_system.m`：主仿真程序（使用tf和lsim函数）
- `system_ode.m`：微分方程函数（备用，当前版本未使用）
- `note_lab12.md`：实验笔记（本文件）
- `lab12_LTI_response_separate.png`：分离显示的仿真结果图像
- `lab12_LTI_response_comparison.png`：输入输出对比图像

## 运行说明

在MATLAB命令窗口中运行：
```matlab
lab12_LTI_system
```

程序将：
1. 显示系统传递函数
2. 使用lsim()进行仿真（步长0.1）
3. 绘制激励信号波形和系统响应波形（带图例）
4. 生成输入输出对比图
5. 输出关键时刻的数值结果
6. 保存两个PNG图像文件
