%% Lab12 - LTI系统响应仿真
% 微分方程: y''(t) + 2y'(t) + y(t) = f'(t) + 2f(t)
% 输入信号: f(t) = e^(-2t)u(t)
% 时间范围: 0-5秒，步长: 0.1

%% 参数设置
t_start = 0;
t_end = 5;
dt = 0.1;                    % 步长设置为0.1
t = t_start:dt:t_end;        % 时间向量，步长0.1

%% 系统函数创建
sys = tf(a, b);              % 调用tf函数生成系统函数对象sys

fprintf('系统传递函数:\n');
disp(sys);

%% 计算输入信号 f(t) = e^(-2t)u(t)
f_input = exp(-2 * t);       % 输入信号

%% 使用lsim函数进行系统仿真
% lsim(sys, f, t) - 系统响应波形仿真
y_response = lsim(sys, f_input, t);

%% 绘制结果
figure('Position', [100, 100, 1200, 800]);

% 子图1: 输入信号波形
subplot(2, 1, 1);
plot(t, f_input, 'b-', 'LineWidth', 2, 'DisplayName', 'f(t) = e^{-2t}u(t)');
grid on;
xlabel('时间 t (秒)');
ylabel('输入信号 f(t)');
title('激励信号波形');
xlim([0, 5]);
legend('show', 'Location', 'northeast');

% 子图2: 系统响应波形
subplot(2, 1, 2);
plot(t, y_response, 'r-', 'LineWidth', 2, 'DisplayName', '系统响应 y(t)');
grid on;
xlabel('时间 t (秒)');
ylabel('系统响应 y(t)');
title('系统响应波形');
xlim([0, 5]);
legend('show', 'Location', 'northeast');

% 调整子图间距
sgtitle('LTI系统响应仿真 - Lab12 (步长dt=0.1)', 'FontSize', 16, 'FontWeight', 'bold');

%% 显示数值结果
fprintf('=== Lab12 LTI系统响应仿真结果 ===\n');
fprintf('微分方程: y''''(t) + 2y''(t) + y(t) = f''(t) + 2f(t)\n');
fprintf('输入信号: f(t) = e^(-2t)u(t)\n');
fprintf('时间范围: 0 - 5 秒，步长: %.1f\n', dt);
fprintf('仿真方法: 使用lsim()函数和tf()传递函数\n');
fprintf('\n传递函数: H(s) = (s + 2) / (s^2 + 2s + 1)\n');
fprintf('\n关键时刻的系统响应值:\n');

% 显示几个关键时刻的值
key_times = [0,1,2,3,4,5 ];
for i = 1:length(key_times)
    % 找到最接近的时间点索引
    [~, idx] = min(abs(t - key_times(i)));
    t_val = t(idx);
    y_val = y_response(idx);
    f_val = f_input(idx);
    fprintf('t = %.1f秒: y(t) = %.6f, f(t) = %.6f\n', t_val, y_val, f_val);
end

%% 保存图像
saveas(figure(1), 'lab12_LTI_response_separate.png');
fprintf('\n图像已保存为:\n');
fprintf('- lab12_LTI_response_separate.png (分离图)\n');
