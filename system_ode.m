function dydt = system_ode(t, y)
%% 系统微分方程函数
% 微分方程: y''(t) + 2y'(t) + y(t) = f'(t) + 2f(t)
% 其中 f(t) = e^(-2t)u(t)
%
% 将二阶微分方程转换为一阶微分方程组:
% 设 y1 = y(t), y2 = y'(t)
% 则: y1' = y2
%     y2' = -2*y2 - y1 + f'(t) + 2*f(t)
%
% 输入参数:
%   t - 时间
%   y - 状态向量 [y1; y2] = [y(t); y'(t)]
% 输出参数:
%   dydt - 状态导数向量 [y1'; y2']

% 状态变量
y1 = y(1);  % y(t)
y2 = y(2);  % y'(t)

% 输入信号及其导数
% f(t) = e^(-2t)u(t), 对于t >= 0
% f'(t) = -2*e^(-2t)u(t), 对于t >= 0
if t >= 0
    f_t = exp(-2*t);        % f(t) = e^(-2t)
    df_dt = -2*exp(-2*t);   % f'(t) = -2*e^(-2t)
else
    f_t = 0;                % u(t) = 0 for t < 0
    df_dt = 0;              % f'(t) = 0 for t < 0
end

% 计算右端项: f'(t) + 2f(t)
rhs = df_dt + 2*f_t;

% 微分方程组
% y1' = y2
% y2' = -2*y2 - y1 + rhs
dydt = zeros(2,1);
dydt(1) = y2;                    % y'(t)
dydt(2) = -2*y2 - y1 + rhs;      % y''(t) = -2y'(t) - y(t) + f'(t) + 2f(t)

end
